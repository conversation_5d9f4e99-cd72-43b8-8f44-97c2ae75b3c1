import { AssetUrl } from '../types/widgetSettings';

// Reward Types
export type RewardType = 'coupon-code' | 'claimable-url';

export interface RewardDefinition {
  id: string;
  name: string;
  description: string;
  image?: AssetUrl;
  type: RewardType;
}

export interface RewardPool {
  id: string;
  name: string;
  rewards: RewardDefinition[];
}

// Game Round Types
export interface GameRoundResult {
  score?: number;
}

export interface GameEndData {
  score?: number;
}

export interface GameEventData {
  roundId: string;
  score?: number;
}



// Reward Roll Types
export interface RewardRoll {
  id: string;
  roundId: string;
  gameWidgetId: string;
  timestamp: number;
  status: 'pending' | 'rolling' | 'completed' | 'failed';
  result?: {
    hasWon: boolean;
    reward?: RewardDefinition;
  };
}

export interface RewardHistory {
  rewardPoolId: string;
  rolls: RewardRoll[];
}

export interface RewardLoadingState {
  isLoading: boolean;
  rollId?: string;
  operation: 'rolling' | 'claiming' | 'fetching' | null;
}

// Simplified Reward Mechanics Types
export interface RewardMechanics {
  triggerOn: 'round_start' | 'round_finish' | 'game_finish';
}

// Hook Return Types
export interface UseRewardsReturn {
  // State
  rewardHistory: RewardHistory;
  loadingState: RewardLoadingState;
  currentRoundId: string;

  // Actions
  handleGameEvent: (
    triggerType: 'round_start' | 'round_finish' | 'game_finish',
    data?: Omit<GameEventData, 'roundId'>
  ) => Promise<RewardRoll | null>;

  // Queries
  getPickResultByRoundId: (roundId: string) => Promise<RewardRoll >;
  getRewardHistory: () => Promise<RewardHistory>;
}
